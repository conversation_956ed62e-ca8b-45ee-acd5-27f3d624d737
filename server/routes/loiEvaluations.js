const express = require('express');
const router = express.Router();
const fs = require('fs').promises;
const path = require('path');
const csv = require('csv-parser');
const { Readable } = require('stream');
const loiGeminiService = require('../services/loiGemini');
const supabase = require('../supabaseClient');

// Background processing function
async function processLoIEvaluationAsync(evaluationId, datasetRows, promptContent) {
  try {
    console.log(`Starting background processing for LoI evaluation ${evaluationId}`);

    // Run the LoI evaluation
    const result = await loiGeminiService.runLoIEvaluation(datasetRows, promptContent);

    // Update the record with the result
    const { error: updateError } = await supabase
      .from('loi_evaluations')
      .update({
        output: result,
        details: result,
        status: 'completed',
        continuous_learning_accuracy: result.summary.continuousLearningAccuracy,
        driving_for_result_accuracy: result.summary.drivingForResultAccuracy,
        total_rows_processed: result.summary.totalRows
      })
      .eq('id', evaluationId);

    if (updateError) {
      console.error('Error updating LoI evaluation:', updateError);
      // Update status to error
      await supabase
        .from('loi_evaluations')
        .update({ status: 'error' })
        .eq('id', evaluationId);
    } else {
      console.log(`LoI evaluation ${evaluationId} completed successfully`);
    }
  } catch (error) {
    console.error(`Error processing LoI evaluation ${evaluationId}:`, error);
    // Update status to error
    await supabase
      .from('loi_evaluations')
      .update({ status: 'error' })
      .eq('id', evaluationId);
  }
}

// Parse CSV data from string
function parseCSVFromString(csvString) {
  return new Promise((resolve, reject) => {
    const results = [];
    const stream = Readable.from([csvString]);
    
    stream
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', (error) => reject(error));
  });
}

// GET all LoI evaluations
router.get('/', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('loi_evaluations')
      .select('*')
      .order('timestamp', { ascending: false });

    if (error) {
      console.error('Error fetching LoI evaluations:', error);
      return res.status(500).json({ error: 'Failed to fetch LoI evaluations' });
    }

    res.json(data);
  } catch (error) {
    console.error('Error in GET /loi-evaluations:', error);
    res.status(500).json({ error: 'Failed to fetch LoI evaluations' });
  }
});

// GET specific LoI evaluation status
router.get('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { data, error } = await supabase
      .from('loi_evaluations')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching LoI evaluation status:', error);
      if (error.code === 'PGRST116') { // No rows found
        return res.status(404).json({ error: 'Evaluation not found' });
      }
      return res.status(500).json({ error: 'Failed to fetch evaluation status' });
    }

    res.json(data);
  } catch (error) {
    console.error('Error in GET /loi-evaluations/:id/status:', error);
    res.status(500).json({ error: 'Failed to fetch evaluation status' });
  }
});

// PUT update LoI evaluation annotation
router.put('/:id/annotation', async (req, res) => {
  try {
    const { id } = req.params;
    const { annotation } = req.body;

    if (!annotation || !['good', 'not good'].includes(annotation)) {
      return res.status(400).json({ error: 'Annotation must be either "good" or "not good"' });
    }

    const { data, error } = await supabase
      .from('loi_evaluations')
      .update({ annotation })
      .eq('id', id)
      .select('*')
      .single();

    if (error) {
      console.error('Error updating LoI evaluation annotation:', error);
      return res.status(500).json({ error: 'Failed to update annotation' });
    }

    res.json(data);
  } catch (error) {
    console.error('Error in PUT /loi-evaluations/:id/annotation:', error);
    res.status(500).json({ error: 'Failed to update annotation' });
  }
});

// POST run new LoI evaluation
router.post('/run', async (req, res) => {
  try {
    const { datasetId } = req.body;

    if (!datasetId) {
      return res.status(400).json({ error: 'Dataset ID is required' });
    }

    // Fetch the dataset
    const { data: dataset, error: datasetError } = await supabase
      .from('datasets')
      .select('*')
      .eq('id', datasetId)
      .single();

    if (datasetError || !dataset) {
      console.error('Error fetching dataset:', datasetError);
      return res.status(400).json({ error: 'Dataset not found' });
    }

    // Validate that the dataset is labeled for LoI evaluation
    if (dataset.label !== 'loi') {
      return res.status(400).json({
        error: 'Invalid dataset type. Please select a dataset labeled for LoI evaluation.'
      });
    }

    // Parse dataset CSV data
    let datasetRows;
    try {
      if (dataset.data && dataset.data.csvData) {
        // Parse CSV data from the dataset
        datasetRows = await parseCSVFromString(dataset.data.csvData);
      } else {
        return res.status(400).json({ error: 'Dataset does not contain CSV data' });
      }
    } catch (parseError) {
      console.error('Error parsing dataset CSV:', parseError);
      return res.status(400).json({ error: 'Invalid CSV data in dataset' });
    }

    if (!datasetRows || datasetRows.length === 0) {
      return res.status(400).json({ error: 'Dataset is empty or invalid' });
    }

    // Validate dataset structure (should have input_text and expected_results columns)
    const firstRow = datasetRows[0];
    if (!firstRow.input_text || !firstRow.expected_results) {
      return res.status(400).json({ 
        error: 'Dataset must have "input_text" and "expected_results" columns' 
      });
    }

    // Read LoI prompt from file
    let promptContent;
    try {
      const promptPath = path.join(__dirname, '../data/loi_prompt.txt');
      promptContent = await fs.readFile(promptPath, 'utf8');
    } catch (fileError) {
      console.error('Error reading LoI prompt file:', fileError);
      return res.status(500).json({ error: 'Failed to load LoI prompt' });
    }

    // Create initial record with "in_progress" status
    const evaluationToInsert = {
      dataset_id: datasetId,
      dataset_name: dataset.name,
      output: null,
      status: 'in_progress',
      prompt_version: 1, // File-based prompt version
      prompt_content: promptContent,
      timestamp: new Date().toISOString(),
      details: null,
      annotation: null,
      continuous_learning_accuracy: null,
      driving_for_result_accuracy: null,
      total_rows_processed: 0
    };

    const { data: newEvaluation, error: insertError } = await supabase
      .from('loi_evaluations')
      .insert([evaluationToInsert])
      .select('*')
      .single();

    if (insertError) {
      console.error('Error inserting LoI evaluation:', insertError);
      return res.status(500).json({ error: 'Failed to save LoI evaluation' });
    }

    // Start background processing
    processLoIEvaluationAsync(newEvaluation.id, datasetRows, promptContent);

    // Return the evaluation record immediately
    res.json(newEvaluation);
  } catch (error) {
    console.error('Error in POST /loi-evaluations/run:', error);
    res.status(500).json({ error: 'Failed to run LoI evaluation' });
  }
});

module.exports = router;
